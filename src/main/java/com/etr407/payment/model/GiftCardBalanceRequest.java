package com.etr407.payment.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import static com.etr407.payment.PaymentApplicationReturnCode.*;
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public record GiftCardBalanceRequest(
    @NotBlank(message = CONSTRAINT_VIOLATION_NOT_BLANK_NAME)
    @Pattern(regexp = "^[0-9]{19}$", message = CONSTRAINT_VIOLATION_PATTERN_NAME)
    @NotNull(message = CONSTRAINT_VIOLATION_NOT_NULL_NAME)
    @Schema(description = "The gift card number", maxLength = 19)
    String cardNumber,
    @Pattern(regexp = "^[0-9]{6}$", message = CONSTRAINT_VIOLATION_PATTERN_NAME)
    @NotNull(message = CONSTRAINT_VIOLATION_NOT_NULL_NAME)
    @Schema(description = "The security code from the gift card", maxLength = 6)
    String securityCode) {
    }
