package com.etr407.payment.model;

import com.etr407.payment.validation.PaymentRequestConstraint;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Digits;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.NonNull;

import java.math.BigDecimal;

import static com.etr407.payment.PaymentApplicationReturnCode.*;

@Builder
@PaymentRequestConstraint
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public record PaymentRequest(

    @Pattern(regexp = "^[0-9]{9}+$|^[0-9]{12}+$", message = CONSTRAINT_VIOLATION_PATTERN_NAME)
    @NotNull(message = CONSTRAINT_VIOLATION_NOT_NULL_NAME)
    @Schema(description = "Uniquely identifies the customer account for which the payment amount will be posted against", maxLength = 12)
    String accountNumber,
    @Digits(integer = 7, fraction = 2, message = CONSTRAINT_VIOLATION_DIGITS_NAME)
    @DecimalMin(value = "0.00", inclusive = false, message = CONSTRAINT_VIOLATION_DECIMAL_MIN_NAME)
    @DecimalMax(value = "5000.00", inclusive = true, message = CONSTRAINT_VIOLATION_DECIMAL_MAX_NAME)
    @NotNull(message = CONSTRAINT_VIOLATION_NOT_NULL_NAME)
    @Schema(description = "The dollar amount of the payment that will be posted")
    BigDecimal amount,
    @NotNull(message = CONSTRAINT_VIOLATION_NOT_NULL_NAME)
    @Schema(description = "The method being used for the payment", maxLength = 11)
    PaymentMethod paymentMethod,
    @Schema(description = "Name of the cardholder as it appears on the card being used for the payment", maxLength = 32)
    String cardHolderName,
    @Schema(description = "The card number from the card being used for the payment. Implementation detail - a token for the card number is expected", maxLength = 19)
    String cardNumber,
    @Schema(description = "Four digit numeric month and year (mmyy) from the card being used for the payment", maxLength = 4)
    String cardExpiry,
    @Schema(description = "The security code (e.g. CVV, PIN) from the card being used for the payment", maxLength = 6)
    String securityCode,
    @Schema(description = "The last four digits of card that was used for the payment", maxLength = 4)
    String cardLastFourDigits,
    @Schema(description = "The type of card that was used for the payment", maxLength = 10)
    PaymentCardType cardType)  {

    //TODO: revisit and clean up.
    public static final String ACCOUNT_NUMBER_PROPERTY_NAME = "accountNumber";
    public static final String AMOUNT_PROPERTY_NAME = "amount";
    public static final String PAYMENT_METHOD_PROPERTY_NAME = "paymentMethod";
    public static final String CARD_HOLDER_NAME_PROPERTY_NAME = "cardHolderName";
    public static final String CARD_NUMBER_PROPERTY_NAME = "cardNumber";
    public static final String CARD_EXPIRY_PROPERTY_NAME = "cardExpiry";
    public static final String SECURITY_CODE_PROPERTY_NAME = "securityCode";
    public static final String CARD_LAST_FOUR_DIGITS_PROPERTY_NAME = "cardLastFourDigits";
    public static final String CARD_TYPE_PROPERTY_NAME = "cardType";
}
