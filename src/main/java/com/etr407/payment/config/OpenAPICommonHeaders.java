package com.etr407.payment.config;

import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Schema;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Parameters({
        @Parameter(
                name = "traceparent",
                description = "Traceparent header from W3C Trace Context.",
                in = ParameterIn.HEADER,
                schema = @Schema(implementation = String.class, maxLength=200)),
        @Parameter(
                name = "tracestate",
                description = "Tracestate header from W3C Trace Context.",
                in = ParameterIn.HEADER,
                schema = @Schema(implementation = String.class, maxLength=200)),
        @Parameter(
                name = "channel",
                description = "Identifies the digital service channel the customer used to submit the request for processing.",
                in = ParameterIn.HEADER,
                schema = @Schema(implementation = String.class, maxLength=30))
})
public @interface OpenAPICommonHeaders {}
