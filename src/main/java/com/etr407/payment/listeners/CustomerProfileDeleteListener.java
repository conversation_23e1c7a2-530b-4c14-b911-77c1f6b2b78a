package com.etr407.payment.listeners;

import com.etr407.event.model.user.CustomerProfileDeleteEvent;
import com.etr407.payment.dao.BillingAccountOwnerDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerProfileDeleteListener {

    private final BillingAccountOwnerDao billingAccountOwnerDao;

    @Transactional
    @JmsListener(destination = "pmt-profile-delete", containerFactory = "SolaceJmsListenerContainerFactory",
        concurrency = "${listeners.customer-profile-delete.concurrency}")
    public void onMessage(CustomerProfileDeleteEvent event) {
        log.info("received customer profile deleted event [{}]", event.digitalId());
        int c = billingAccountOwnerDao.deleteByDigitalId(event.digitalId());
        log.info("deleted {} digital users for digital id [{}]", c, event.digitalId());
    }
}
