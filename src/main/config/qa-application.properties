spring.datasource.url=jdbc:aws-wrapper:postgresql://aurora-postgresql-proxy.proxy-cipmrrvl5okb.ca-central-1.rds.amazonaws.com/payment_qa?sslmode=require
spring.datasource.username=payment-service_qa

solace.jms.host=tcps://407etr-qa-1.messaging.solace.cloud:55443
solace.jms.msg-vpn=solace-407etr-qa-1
solace.jms.client-username=solace-cloud-client

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://qat.auth.407etr.com/

integration-token-service.host=http://integration-api-oauth-token.qa-integration:8080
spring.security.oauth2.client.registration.esb-billing-account.client-id=<EMAIL>
spring.security.oauth2.client.registration.esb-billing-customer.client-id=<EMAIL>

esb.host=http://esb-qat.407etr.com/apis

gateway.endPointPrimaryBaseUrl=https://staging.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseUrl=https://staging.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointPrimaryBaseProxyUrl=https://outbound-407etr-staging-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseProxyUrl=https://outbound-407etr-staging-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.web.terminalId=MPD00492
gateway.connectionTimeoutMs=3000
gateway.connectReadTimeoutMs=72000

# Swagger
springdoc.api-docs.path=/api-docs
