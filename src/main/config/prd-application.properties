spring.datasource.url=jdbc:aws-wrapper:postgresql://aurora-postgresql-proxy.proxy-cosezhzbffj5.ca-central-1.rds.amazonaws.com/payment_prd?sslmode=require
spring.datasource.username=payment-service_prd

solace.jms.host=tcps://407etr-prd-1.messaging.solace.cloud:55443
solace.jms.msg-vpn=solace-407etr-prd-1
solace.jms.client-username=solace-cloud-client

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://auth.407etr.com/

integration-token-service.host=http://integration-api-oauth-token.prd-integration:8080
spring.security.oauth2.client.registration.esb-billing-account.client-id=<EMAIL>
spring.security.oauth2.client.registration.esb-billing-customer.client-id=<EMAIL>

esb.host=http://esb.407etr.com/apis

gateway.endPointPrimaryBaseUrl=https://ms1.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseUrl=https://ms2.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointPrimaryBaseProxyUrl=https://outbound-407etr-prod-ms1-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseProxyUrl=https://outbound-407etr-prod-ms2-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.web.terminalId=ETRWEB66
gateway.connectionTimeoutMs=3000
gateway.connectReadTimeoutMs=72000

#listeners
listeners.linked-accounts.concurrency=5
listeners.customer-profile-delete.concurrency=2

# Swagger
springdoc.api-docs.path=/api-docs

