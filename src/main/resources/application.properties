server.port=8080
spring.application.name=payment-service
server.servlet.context-path=/payments

# logging
logging.pattern.console=%d{${LOG_DATEFORMAT_PATTERN:yyyy-MM-dd'T'HH:mm:ss.SSSXXX}} ${LOG_LEVEL_PATTERN:%5p} [%X{digitalId}] ${LOG_CORRELATION_PATTERN} [%thread] %logger{30}: %m%n${LOG_EXCEPTION_CONVERSION_WORD:%wEx}
logging.level.org.springframework.web.filter.CommonsRequestLoggingFilter=DEBUG

# actuator
management.server.port=8081
management.endpoint.info.enabled=true
management.info.java.enabled=true
management.info.build.enabled=true
management.info.env.enabled=true
management.endpoint.health.enabled=true
management.endpoints.web.exposure.include=health,info,prometheus

management.tracing.baggage.correlation.fields=digitalId

# solace/jms
solace.jms.host=tcp://localhost:55554
solace.jms.msg-vpn=default
solace.jms.client-username=default
solace.jms.client-password=default

spring.jms.pub-sub-domain=true
spring.jms.cache.session-cache-size=5

# integration token service
integration-token-service.host=http://integration-api-oauth-token.dev-integration:8080

spring.security.oauth2.resourceserver.jwt.audiences=https://digital.407etr.com

spring.security.oauth2.client.registration.esb-billing-account.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.esb-billing-account.client-id=<EMAIL>
spring.security.oauth2.client.registration.esb-billing-account.scope=erp:billing
spring.security.oauth2.client.registration.esb-billing-account.client-authentication-method=client_secret_post
spring.security.oauth2.client.provider.esb-billing-account.token-uri=${integration-token-service.host}/apis/integration/oauth2/v2/token

spring.security.oauth2.client.registration.esb-billing-customer.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.esb-billing-customer.client-id=<EMAIL>
spring.security.oauth2.client.registration.esb-billing-customer.scope=erp:customer
spring.security.oauth2.client.registration.esb-billing-customer.client-authentication-method=client_secret_post
spring.security.oauth2.client.provider.esb-billing-customer.token-uri=${integration-token-service.host}/apis/integration/oauth2/v2/token

# esb
esb.host=http://esb-dev.407etr.com/apis

# gateway
gateway.endPointPrimaryBaseUrl=https://staging.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseUrl=https://staging.eigendev.com/OFT/EigenOFT_p.php
gateway.endPointPrimaryBaseProxyUrl=https://outbound-407etr-staging-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.endPointSecondaryBaseProxyUrl=https://outbound-407etr-staging-eigendev-com.vaulting.io/OFT/EigenOFT_p.php
gateway.web.terminalId=MPD00492
gateway.connectionTimeoutMs=3000
gateway.connectReadTimeoutMs=72000
gateway.defaultGiftCardExpiryDate=1299

# database
spring.datasource.driver-class-name=software.amazon.jdbc.Driver
spring.datasource.hikari.data-source-properties.wrapperDialect=aurora-pg
spring.datasource.hikari.data-source-properties.wrapperPlugins=iam

mybatis.config-location=mybatis-config.xml

# recaptcha
recaptcha.secret-key=test-secret
recaptcha.base-url=https://www.google.com/recaptcha/api/
recaptcha.score-threshold-v3=0.5

# listeners
listeners.linked-accounts.concurrency=1
listeners.customer-profile-delete.concurrency=1

# Swagger
springdoc.api-docs.path=/api-docs