spring.cloud.config.enabled=false
server.port=8084
management.server.port=${server.port}

spring.security.oauth2.resourceserver.jwt.issuer-uri=https://dev.auth.407etr.com/

# database
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.url=****************************************
spring.datasource.username=postgres
spring.datasource.password=postgres

# integration token service
integration-token-service.host=http://localhost:8099

spring.security.oauth2.client.registration.esb-billing-account.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.esb-billing-account.client-id=test-client_id
spring.security.oauth2.client.registration.esb-billing-account.client-secret=test-client-secret
spring.security.oauth2.client.registration.esb-billing-account.scope=erp:billing
spring.security.oauth2.client.registration.esb-billing-account.client-authentication-method=client_secret_post
spring.security.oauth2.client.provider.esb-billing-account.token-uri=${integration-token-service.host}/apis/integration/oauth2/v2/token

spring.security.oauth2.client.registration.esb-billing-customer.authorization-grant-type=client_credentials
spring.security.oauth2.client.registration.esb-billing-customer.client-id=test-client_id
spring.security.oauth2.client.registration.esb-billing-customer.client-secret=test-client-secret
spring.security.oauth2.client.registration.esb-billing-customer.scope=erp:customer
spring.security.oauth2.client.registration.esb-billing-customer.client-authentication-method=client_secret_post
spring.security.oauth2.client.provider.esb-billing-customer.token-uri=${integration-token-service.host}/apis/integration/oauth2/v2/token

# esb
esb.host=http://localhost:8099

# payment gateway
gateway.endPointPrimaryBaseUrl=http://localhost:8099/wiremock/eigen
gateway.endPointSecondaryBaseUrl=http://localhost:8099/wiremock/eigen-secondary
gateway.endPointPrimaryBaseProxyUrl=http://localhost:8099/wiremock/eigen
gateway.endPointSecondaryBaseProxyUrl=http://localhost:8099/wiremock/eigen-secondary
gateway.web.terminalId=MPD00492
gateway.web.hashPassword=uwhoqbckb2x7aoca6cbx5nribu4w6huqf283abww
gateway.connectionTimeoutMs=3000
gateway.connectReadTimeoutMs=72000
gateway.defaultGiftCardExpiryDate=1299

# recaptcha
recaptcha.secret-key=test-secret
recaptcha.secret-key-v3=test-secret
recaptcha.base-url=http://localhost:8099/recaptcha
