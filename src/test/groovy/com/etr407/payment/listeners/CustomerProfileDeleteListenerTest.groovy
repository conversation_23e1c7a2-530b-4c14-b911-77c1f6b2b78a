package com.etr407.payment.listeners

import com.etr407.event.model.user.CustomerProfileDeleteEvent
import com.etr407.model.id.DigitalId
import com.etr407.payment.dao.BillingAccountOwnerDao
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension

import static org.assertj.core.api.Assertions.assertThat
import static org.assertj.core.api.Assertions.catchThrowableOfType
import static org.mockito.Mockito.times
import static org.mockito.Mockito.verify
import static org.mockito.Mockito.verifyNoMoreInteractions
import static org.mockito.Mockito.when

@ExtendWith(MockitoExtension)
class CustomerProfileDeleteListenerTest {
    @InjectMocks
    CustomerProfileDeleteListener fixture
    @Mock
    BillingAccountOwnerDao billingAccountOwnerDao

    @Test
    void 'on message customer profile delete event'() {

        def digitalId = DigitalId.of('c4a50ba2-8600-4d7b-961f-64de87c2d3fd')
        def event = CustomerProfileDeleteEvent.builder().digitalId(digitalId).build()

        fixture.onMessage(event)
        verify(billingAccountOwnerDao, times(1)).deleteByDigitalId(digitalId)
        verifyNoMoreInteractions(billingAccountOwnerDao)
    }

    @Test
    void 'on message claimed account dao raised runtime exception during remove claimed account'() {

        def digitalId = DigitalId.of('c4a50ba2-8600-4d7b-961f-64de87c2d3fd')
        def event = CustomerProfileDeleteEvent.builder().digitalId(digitalId).build()
        def expected = new RuntimeException('horrible things happen to horrible people, sometimes')
        when(billingAccountOwnerDao.deleteByDigitalId(digitalId)).thenThrow(expected)

        def actual = catchThrowableOfType(() -> fixture.onMessage(event), RuntimeException.class)
        assertThat(actual).usingRecursiveComparison().isEqualTo(expected)
        verify(billingAccountOwnerDao, times(1)).deleteByDigitalId(digitalId)
        verifyNoMoreInteractions(billingAccountOwnerDao)
    }
}
