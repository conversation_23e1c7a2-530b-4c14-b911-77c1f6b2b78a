package com.etr407.cucumber.steps;

import com.etr407.model.id.DigitalId;
import com.etr407.payment.dao.BillingAccountOwnerDao;
import com.etr407.payment.model.BillingAccountOwner;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import lombok.RequiredArgsConstructor;

import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RequiredArgsConstructor
public class DatabaseSteps {

    private final BillingAccountOwnerDao billingAccountOwnerDao;

    @Given("no billing account owner with digital id {}")
    public void verifyNoClaimedAccountWithDigitalId(String digitalId) {
        List<BillingAccountOwner> billingAccountOwners = billingAccountOwnerDao.selectByDigitalId(DigitalId.of(digitalId));
        assertThat(billingAccountOwners.isEmpty()).isTrue();
    }

    @Given("found billing account owner with digital id {}")
    public void verifyFoundBillingAccountOwnerWithDigitalId(String digitalId) {
        List<BillingAccountOwner> billingAccountOwners = billingAccountOwnerDao.selectByDigitalId(DigitalId.of(digitalId));
        assertThat(billingAccountOwners.isEmpty()).isFalse();
    }

    @Then("verify billing account owner with digital id {} has been removed")
    public void verifyClaimedAccountWithDigitalIdHasBeenRemoved(String digitalId) {
        List<BillingAccountOwner> claimedAccount = billingAccountOwnerDao.selectByDigitalId(DigitalId.of(digitalId));
        assertThat(claimedAccount.isEmpty()).isTrue();
    }
}
